{"expo": {"name": "PocketMini", "slug": "pocketmini", "version": "1.0.0", "orientation": "portrait", "owner": "iamsankalp", "icon": "./assets/images/icon.png", "scheme": "com.sankalp.pocketmini", "userInterfaceStyle": "automatic", "newArchEnabled": false, "ios": {"supportsTablet": true, "bundleIdentifier": "com.sankalp.pocketmini", "icon": "./assets/images/icon.png"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/icon.png", "backgroundColor": "#ffffff"}, "package": "com.sankalp.pocketmini", "googleServicesFile": "./google-services.json", "permissions": ["android.permission.RECORD_AUDIO"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/icon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-image-picker", {"photosPermission": "We need access to your photo library to let you update your profile picture."}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "37788101-e47b-4379-8d0a-3e09bb236aab"}}}}